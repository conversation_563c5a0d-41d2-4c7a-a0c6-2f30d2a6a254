# Internal Training System

A comprehensive web application for hosting cybersecurity training videos with multilingual support, interactive assessments, and certificate generation.

## Features

### Core Functionality
- **Multilingual Video Training**: Support for multiple languages with seamless switching
- **Interactive Assessments**: Multiple choice, true/false, and short answer questions
- **Certificate Generation**: Automatic digital certificates upon successful completion
- **Progress Tracking**: Comprehensive user progress monitoring
- **SOC Compliance Reporting**: Detailed reports for compliance requirements

### User Management
- **Bulk User Creation**: Import users via semicolon-separated email lists
- **Random Password Generation**: Automatic secure password generation
- **Role-Based Access**: Admin and regular user roles
- **Session Management**: Secure authentication system

### Admin Features
- **Training Module Management**: Create and manage training content
- **Video Upload**: Support for multiple video formats up to 500MB
- **Quiz Builder**: Google Forms-like interface for creating assessments
- **Analytics Dashboard**: Comprehensive reporting and statistics
- **User Administration**: Manage users, reset passwords, view progress

## Technology Stack

### Frontend
- **React 18** with Vite for fast development
- **Ant Design** for UI components
- **React Router** for navigation
- **React Query** for data fetching
- **React Player** for video playback

### Backend
- **Node.js** with Express.js
- **PostgreSQL** database
- **Session-based authentication**
- **Multer** for file uploads
- **Joi** for validation

### Infrastructure
- **Docker** containerization
- **Nginx** reverse proxy
- **Local storage** with Azure Blob capability

## Quick Start

### Prerequisites
- Docker and Docker Compose

### Docker Deployment

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd internal-training-system
   ```

2. **Start the application**
   ```bash
   npm run docker:up
   # or
   docker-compose up --build
   ```

3. **Access the application**
   - Frontend: http://localhost:3001
   - Backend API: http://localhost:3002

4. **Default Admin Login**
   - Email: `<EMAIL>`
   - Password: `admin123`

### Docker Commands

```bash
# Start services
npm run docker:up

# Stop services
npm run docker:down

# View logs
npm run docker:logs

# Restart services
npm run docker:restart

# Test setup
npm run test
```

## Configuration

### Environment Variables

#### Server (.env)
```env
NODE_ENV=development
PORT=3001
DB_HOST=localhost
DB_PORT=5432
DB_NAME=training_system
DB_USER=training_user
DB_PASSWORD=training_password
SESSION_SECRET=your_super_secret_session_key
```

#### Docker Environment
The docker-compose.yml file contains all necessary environment variables for containerized deployment.

## Usage Guide

### For Administrators

1. **User Management**
   - Navigate to Admin → User Management
   - Use "Bulk Create Users" to import email lists
   - Copy generated passwords and distribute to users

2. **Creating Training Modules**
   - Go to Admin → Module Management
   - Create new training module
   - Upload videos for different languages
   - Create corresponding quizzes

3. **Quiz Creation**
   - Navigate to Admin → Quiz Management
   - Select module and language
   - Add questions with multiple choice or true/false options
   - Set passing score (default: 70%)

4. **Reporting**
   - Access Admin → Reports & Analytics
   - Generate SOC compliance reports
   - Export reports in HTML format
   - View completion statistics

### For Users

1. **Taking Training**
   - Login with provided credentials
   - Select training module from dashboard
   - Choose language preference
   - Watch video (cannot skip forward, can rewind)
   - Complete quiz after video
   - Download certificate upon passing

2. **Progress Tracking**
   - View progress on dashboard
   - Check completion status
   - Review quiz scores
   - Access certificates

## API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user
- `POST /api/auth/change-password` - Change password

### Training Endpoints
- `GET /api/training/modules` - Get all modules
- `GET /api/training/modules/:id` - Get specific module
- `POST /api/training/modules` - Create module (admin)
- `POST /api/training/modules/:id/videos` - Upload video (admin)

### Quiz Endpoints
- `GET /api/quiz/modules/:moduleId/quiz/:languageCode` - Get quiz
- `POST /api/quiz/modules/:moduleId/quiz/:languageCode/submit` - Submit answers
- `POST /api/quiz/create` - Create quiz (admin)

### User Management Endpoints
- `GET /api/users` - Get all users (admin)
- `POST /api/users/bulk-create` - Bulk create users (admin)
- `PUT /api/users/:email` - Update user (admin)
- `DELETE /api/users/:email` - Delete user (admin)

### Reports Endpoints
- `GET /api/reports/soc-compliance` - SOC compliance report
- `GET /api/reports/completion-stats` - Completion statistics
- `GET /api/reports/user-activity` - User activity report

## Security Features

- **Session-based authentication** with secure cookies
- **Password hashing** using bcrypt
- **Rate limiting** to prevent abuse
- **Input validation** using Joi
- **SQL injection protection** with parameterized queries
- **XSS protection** with security headers
- **File upload validation** with type and size limits

## Deployment

### Production Deployment

1. **Update environment variables** for production
2. **Build and deploy with Docker**
   ```bash
   docker-compose -f docker-compose.yml up -d
   ```
3. **Setup SSL/TLS** with reverse proxy (nginx/Apache)
4. **Configure backup** for PostgreSQL database
5. **Setup monitoring** and logging

### Azure Blob Storage (Optional)

To enable Azure Blob Storage for video files:

1. **Update server/.env**
   ```env
   AZURE_STORAGE_CONNECTION_STRING=your_connection_string
   AZURE_STORAGE_CONTAINER_NAME=training-videos
   ```

2. **Restart the application**

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check PostgreSQL is running
   - Verify database credentials
   - Ensure database exists

2. **Video Upload Fails**
   - Check file size (max 500MB)
   - Verify file format is supported
   - Ensure uploads directory has write permissions

3. **Login Issues**
   - Verify user exists in database
   - Check password is correct
   - Clear browser cookies/session

### Logs

- **Application logs**: Check Docker container logs
- **Database logs**: Check PostgreSQL logs
- **Nginx logs**: Check nginx container logs

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## License

This project is licensed under the MIT License.

## Support

For technical support or questions:
- Check the troubleshooting section
- Review application logs
- Contact system administrator

# Internal Training System Setup Script for Windows PowerShell

Write-Host "=== Internal Training System Setup ===" -ForegroundColor Green
Write-Host ""

# Check if Node.js is installed
Write-Host "Checking Node.js installation..." -ForegroundColor Yellow
$nodeCheck = Get-Command node -ErrorAction SilentlyContinue
if ($nodeCheck) {
    $nodeVersion = node --version
    Write-Host "Node.js found: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "Node.js not found. Please install Node.js 18+ from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if PostgreSQL is available
Write-Host "Checking PostgreSQL..." -ForegroundColor Yellow
$pgCheck = Get-Command psql -ErrorAction SilentlyContinue
if ($pgCheck) {
    $pgVersion = psql --version
    Write-Host "PostgreSQL found: $pgVersion" -ForegroundColor Green
} else {
    Write-Host "PostgreSQL not found. You'll need to install PostgreSQL or use Docker." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Installing dependencies..." -ForegroundColor Yellow

# Install root dependencies
Write-Host "Installing root dependencies..." -ForegroundColor Cyan
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install root dependencies" -ForegroundColor Red
    exit 1
}

# Install server dependencies
Write-Host "Installing server dependencies..." -ForegroundColor Cyan
Set-Location server
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install server dependencies" -ForegroundColor Red
    exit 1
}
Set-Location ..

# Install client dependencies
Write-Host "Installing client dependencies..." -ForegroundColor Cyan
Set-Location client
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to install client dependencies" -ForegroundColor Red
    exit 1
}
Set-Location ..

Write-Host ""
Write-Host "Dependencies installed successfully!" -ForegroundColor Green

# Create uploads directory
Write-Host "Creating uploads directory..." -ForegroundColor Yellow
if (!(Test-Path "server/uploads")) {
    New-Item -ItemType Directory -Path "server/uploads" -Force | Out-Null
    New-Item -ItemType Directory -Path "server/uploads/videos" -Force | Out-Null
    Write-Host "Uploads directory created" -ForegroundColor Green
} else {
    Write-Host "Uploads directory already exists" -ForegroundColor Green
}

Write-Host ""
Write-Host "=== Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Setup PostgreSQL database:" -ForegroundColor White
Write-Host "   - Create database: createdb training_system" -ForegroundColor Gray
Write-Host "   - Update server/.env with your database credentials" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Start the application:" -ForegroundColor White
Write-Host "   - Development: npm run dev" -ForegroundColor Gray
Write-Host "   - Production: npm run build then npm start" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Access the application:" -ForegroundColor White
Write-Host "   - Frontend: http://localhost:3000 (development)" -ForegroundColor Gray
Write-Host "   - Backend API: http://localhost:3001" -ForegroundColor Gray
Write-Host ""
Write-Host "4. Default admin login:" -ForegroundColor White
Write-Host "   - Email: <EMAIL>" -ForegroundColor Gray
Write-Host "   - Password: admin123" -ForegroundColor Gray
Write-Host ""
Write-Host "For Docker deployment, run: docker-compose up --build" -ForegroundColor Cyan

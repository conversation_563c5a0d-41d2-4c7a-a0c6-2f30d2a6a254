import React from 'react'
import { <PERSON>, Row, Col, Typography, Statistic, Spin, Alert } from 'antd'
import { UserOutlined, BookOutlined, TrophyOutlined, FileTextOutlined } from '@ant-design/icons'
import { useQuery } from 'react-query'
import { reportsAPI, usersAPI, trainingAPI, handleAPIError } from '../../services/api'

const { Title } = Typography

function AdminDashboard() {
  // Fetch completion statistics
  const { data: statsData, isLoading: statsLoading, error: statsError } = useQuery(
    'completion-stats',
    () => reportsAPI.getCompletionStats(),
    {
      select: (response) => response.data.stats,
    }
  )

  // Fetch all users
  const { data: usersData, isLoading: usersLoading, error: usersError } = useQuery(
    'all-users',
    usersAPI.getAll,
    {
      select: (response) => response.data.users,
    }
  )

  // Fetch training modules
  const { data: modulesData, isLoading: modulesLoading, error: modulesError } = useQuery(
    'training-modules',
    trainingAPI.getModules,
    {
      select: (response) => response.data.modules,
    }
  )

  const isLoading = statsLoading || usersLoading || modulesLoading
  const error = statsError || usersError || modulesError

  if (isLoading) {
    return (
      <div className="loading-spinner">
        <Spin size="large" />
        <span className="loading-text">Loading dashboard...</span>
      </div>
    )
  }

  if (error) {
    const errorInfo = handleAPIError(error)
    return (
      <Alert
        message="Error Loading Dashboard"
        description={errorInfo.message}
        type="error"
        showIcon
      />
    )
  }

  const stats = statsData || []
  const users = usersData || []
  const modules = modulesData || []

  // Calculate overall statistics
  const totalUsers = users.length
  const totalModules = modules.length
  const totalCertificates = stats.reduce((sum, stat) => sum + (stat.certificates_issued || 0), 0)
  const averageCompletionRate = stats.length > 0 
    ? Math.round(stats.reduce((sum, stat) => {
        const rate = stat.total_enrolled_users > 0 
          ? (stat.users_passed_quiz / stat.total_enrolled_users) * 100 
          : 0
        return sum + rate
      }, 0) / stats.length)
    : 0

  return (
    <div>
      <Title level={2}>Admin Dashboard</Title>
      
      {/* Overview Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 32 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Users"
              value={totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Training Modules"
              value={totalModules}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Certificates Issued"
              value={totalCertificates}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Avg Completion Rate"
              value={averageCompletionRate}
              suffix="%"
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Module Statistics */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="Training Module Statistics">
            {stats.length === 0 ? (
              <div style={{ textAlign: 'center', padding: 40 }}>
                <BookOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <Title level={4} type="secondary">No Training Data</Title>
              </div>
            ) : (
              <div style={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{ borderBottom: '2px solid #f0f0f0' }}>
                      <th style={{ padding: '12px 8px', textAlign: 'left' }}>Module</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>Languages</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>Enrolled</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>Completed</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>Certificates</th>
                      <th style={{ padding: '12px 8px', textAlign: 'center' }}>Avg Score</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stats.map((stat, index) => (
                      <tr key={stat.module_id} style={{ borderBottom: '1px solid #f0f0f0' }}>
                        <td style={{ padding: '12px 8px' }}>
                          <strong>{stat.module_title}</strong>
                        </td>
                        <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                          {stat.available_languages || 0}
                        </td>
                        <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                          {stat.total_enrolled_users || 0}
                        </td>
                        <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                          {stat.users_passed_quiz || 0}
                        </td>
                        <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                          {stat.certificates_issued || 0}
                        </td>
                        <td style={{ padding: '12px 8px', textAlign: 'center' }}>
                          {stat.average_score ? `${stat.average_score}%` : '-'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="Recent Activity" style={{ marginBottom: 16 }}>
            <div style={{ textAlign: 'center', padding: 20 }}>
              <FileTextOutlined style={{ fontSize: 32, color: '#d9d9d9', marginBottom: 8 }} />
              <div style={{ color: '#8c8c8c' }}>
                Activity tracking coming soon
              </div>
            </div>
          </Card>

          <Card title="System Health">
            <div style={{ textAlign: 'center', padding: 20 }}>
              <div style={{ 
                width: 60, 
                height: 60, 
                borderRadius: '50%', 
                background: '#52c41a',
                margin: '0 auto 12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: 24,
                fontWeight: 'bold'
              }}>
                ✓
              </div>
              <div style={{ color: '#52c41a', fontWeight: 'bold' }}>
                All Systems Operational
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default AdminDashboard

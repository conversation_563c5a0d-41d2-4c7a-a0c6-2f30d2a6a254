{"name": "internal-training-system", "version": "1.0.0", "description": "Internal Training System for Cybersecurity Videos", "main": "server/index.js", "scripts": {"docker:up": "docker-compose up --build", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose down && docker-compose up --build", "test": "node test-setup.js"}, "keywords": ["training", "cybersecurity", "video", "assessment"], "author": "Internal Training Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}
# 🚀 Quick Start Guide - Internal Training System

## ✅ Setup Complete!

Your Internal Training System has been successfully set up! All dependencies are installed and the project structure is ready.

## 🎯 What's Next?

### Option 1: Development Setup (Recommended for Testing)

1. **Setup PostgreSQL Database**
   ```bash
   # Install PostgreSQL if not already installed
   # Windows: Download from https://www.postgresql.org/download/windows/
   # macOS: brew install postgresql
   # Linux: sudo apt-get install postgresql

   # Create database
   createdb training_system
   
   # Create user (optional)
   createuser training_user
   ```

2. **Configure Database Connection**
   - Open `server/.env`
   - Update database credentials:
   ```env
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=training_system
   DB_USER=your_username
   DB_PASSWORD=your_password
   ```

3. **Start Development Servers**
   ```bash
   npm run dev
   ```
   This starts both frontend (port 3000) and backend (port 3001)

4. **Access the Application**
   - Open: http://localhost:3000
   - Login with: <EMAIL> / admin123

### Option 2: Docker Setup (Production-like)

If you have Docker installed:
```bash
docker-compose up --build
```
- Access at: http://localhost:8390
- Login with: <EMAIL> / admin123

## 🎮 First Steps After Login

### As Administrator:

1. **Create Training Module**
   - Go to Admin → Module Management
   - Click "Create Module"
   - Add title and description

2. **Upload Training Video**
   - In Module Management, click "Upload Video"
   - Select language and upload video file (max 500MB)

3. **Create Quiz**
   - Go to Admin → Quiz Management
   - Select module and language
   - Add questions (Multiple Choice, True/False, Short Answer)
   - Set passing score (default: 70%)

4. **Add Users**
   - Go to Admin → User Management
   - Click "Bulk Create Users"
   - Enter email addresses separated by semicolons
   - Copy generated passwords and send to users

### As User:

1. **Take Training**
   - View available modules on Dashboard
   - Select a module and language
   - Watch video (cannot skip forward)
   - Take quiz after video completion
   - Download certificate if passed

## 📊 Key Features Available

### ✅ Video Training
- Multilingual support
- No skip-forward restriction
- Progress tracking
- Auto-save functionality

### ✅ Interactive Quizzes
- Multiple choice questions
- True/False questions
- Short answer questions (manual grading)
- Configurable passing scores
- Multiple attempts allowed

### ✅ Certificate Generation
- Automatic generation upon passing
- Downloadable and printable
- Unique certificate numbers

### ✅ Admin Features
- User management with bulk creation
- Training module management
- Video upload system
- Quiz builder interface
- SOC compliance reporting

### ✅ Reporting & Analytics
- Training completion statistics
- User activity tracking
- SOC compliance reports (HTML export)
- Progress monitoring

## 🔧 Troubleshooting

### Common Issues:

1. **Database Connection Error**
   ```
   Solution: Check PostgreSQL is running and credentials in server/.env are correct
   ```

2. **Port Already in Use**
   ```
   Solution: Stop other applications using ports 3000/3001 or change ports in package.json
   ```

3. **Video Upload Fails**
   ```
   Solution: Check file size (<500MB) and format (MP4, AVI, MOV, etc.)
   ```

4. **Login Issues**
   ```
   Solution: Use default admin credentials: <EMAIL> / admin123
   ```

### Getting Help:

- Check `README.md` for detailed documentation
- Run `node test-setup.js` to verify setup
- Check browser console for frontend errors
- Check terminal output for backend errors

## 📚 Documentation

- **README.md**: Complete setup and API documentation
- **IMPLEMENTATION_SUMMARY.md**: Technical architecture overview
- **Database Schema**: See `server/database/init.sql`

## 🎉 You're Ready!

Your Internal Training System is now fully functional with:
- ✅ Modern React frontend with Ant Design
- ✅ Robust Node.js backend with PostgreSQL
- ✅ Complete user and admin interfaces
- ✅ Video training with restrictions
- ✅ Interactive quiz system
- ✅ Certificate generation
- ✅ SOC compliance reporting
- ✅ Docker containerization

Start by logging in as admin and creating your first training module! 🚀
